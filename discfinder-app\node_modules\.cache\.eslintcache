[{"C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts": "4", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx": "6", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx": "7", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AdminBulkTurnins.tsx": "8", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\RakerDiverDashboard.tsx": "9", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileManager.tsx": "10", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileImportManager.tsx": "11", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AvatarUpload.tsx": "12", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\profileImport.ts": "13", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\PhotoMigrationManager.tsx": "14", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\photoMigration.ts": "15", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\SourceManager.tsx": "16", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ContactAttempts.tsx": "17"}, {"size": 554, "mtime": 1751073000293, "results": "18", "hashOfConfig": "19"}, {"size": 425, "mtime": 1751073000018, "results": "20", "hashOfConfig": "19"}, {"size": 45553, "mtime": 1751305691690, "results": "21", "hashOfConfig": "19"}, {"size": 21055, "mtime": 1751305919004, "results": "22", "hashOfConfig": "19"}, {"size": 12456, "mtime": 1751124235611, "results": "23", "hashOfConfig": "19"}, {"size": 6258, "mtime": 1751083217163, "results": "24", "hashOfConfig": "19"}, {"size": 4373, "mtime": 1751086289968, "results": "25", "hashOfConfig": "19"}, {"size": 17733, "mtime": 1751120034997, "results": "26", "hashOfConfig": "19"}, {"size": 15916, "mtime": 1751119931275, "results": "27", "hashOfConfig": "19"}, {"size": 10342, "mtime": 1751289983317, "results": "28", "hashOfConfig": "19"}, {"size": 8656, "mtime": 1751121281468, "results": "29", "hashOfConfig": "19"}, {"size": 6035, "mtime": 1751121314460, "results": "30", "hashOfConfig": "19"}, {"size": 6808, "mtime": 1751121248928, "results": "31", "hashOfConfig": "19"}, {"size": 9524, "mtime": 1751289718752, "results": "32", "hashOfConfig": "19"}, {"size": 9028, "mtime": 1751290059629, "results": "33", "hashOfConfig": "19"}, {"size": 12114, "mtime": 1751294455192, "results": "34", "hashOfConfig": "19"}, {"size": 12620, "mtime": 1751305960447, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ncyc5o", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts", ["87", "88", "89", "90"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AdminBulkTurnins.tsx", ["91", "92"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\RakerDiverDashboard.tsx", ["93", "94"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileManager.tsx", [], ["95"], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileImportManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AvatarUpload.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\profileImport.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\PhotoMigrationManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\photoMigration.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\SourceManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ContactAttempts.tsx", [], ["96"], {"ruleId": "97", "severity": 1, "message": "98", "line": 321, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 321, "endColumn": 19}, {"ruleId": "97", "severity": 1, "message": "98", "line": 335, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 335, "endColumn": 19}, {"ruleId": "97", "severity": 1, "message": "98", "line": 415, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 415, "endColumn": 19}, {"ruleId": "97", "severity": 1, "message": "98", "line": 456, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 456, "endColumn": 19}, {"ruleId": "97", "severity": 1, "message": "101", "line": 73, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 73, "endColumn": 22}, {"ruleId": "97", "severity": 1, "message": "98", "line": 99, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 99, "endColumn": 19}, {"ruleId": "97", "severity": 1, "message": "98", "line": 81, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 81, "endColumn": 19}, {"ruleId": "97", "severity": 1, "message": "101", "line": 120, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 120, "endColumn": 22}, {"ruleId": "102", "severity": 1, "message": "103", "line": 30, "column": 6, "nodeType": "104", "endLine": 30, "endColumn": 14, "suggestions": "105", "suppressions": "106"}, {"ruleId": "102", "severity": 1, "message": "107", "line": 18, "column": 6, "nodeType": "104", "endLine": 18, "endColumn": 14, "suggestions": "108", "suppressions": "109"}, "@typescript-eslint/no-unused-vars", "'data' is assigned a value but never used.", "Identifier", "unusedVar", "'success' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["110"], ["111"], "React Hook useEffect has a missing dependency: 'loadContactAttempts'. Either include it or remove the dependency array.", ["112"], ["113"], {"desc": "114", "fix": "115"}, {"kind": "116", "justification": "117"}, {"desc": "118", "fix": "119"}, {"kind": "116", "justification": "117"}, "Update the dependencies array to be: [loadProfile, userId]", {"range": "120", "text": "121"}, "directive", "", "Update the dependencies array to be: [discId, loadContactAttempts]", {"range": "122", "text": "123"}, [904, 912], "[loadProfile, userId]", [647, 655], "[discId, loadContactAttempts]"]