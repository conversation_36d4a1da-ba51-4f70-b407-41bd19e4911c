[{"C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts": "4", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx": "6", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx": "7", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AdminBulkTurnins.tsx": "8", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\RakerDiverDashboard.tsx": "9", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileManager.tsx": "10", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileImportManager.tsx": "11", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AvatarUpload.tsx": "12", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\profileImport.ts": "13", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\PhotoMigrationManager.tsx": "14", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\photoMigration.ts": "15", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\SourceManager.tsx": "16"}, {"size": 554, "mtime": 1751073000293, "results": "17", "hashOfConfig": "18"}, {"size": 425, "mtime": 1751073000018, "results": "19", "hashOfConfig": "18"}, {"size": 44653, "mtime": 1751293166768, "results": "20", "hashOfConfig": "18"}, {"size": 19075, "mtime": 1751293031493, "results": "21", "hashOfConfig": "18"}, {"size": 12456, "mtime": 1751124235611, "results": "22", "hashOfConfig": "18"}, {"size": 6258, "mtime": 1751083217163, "results": "23", "hashOfConfig": "18"}, {"size": 4373, "mtime": 1751086289968, "results": "24", "hashOfConfig": "18"}, {"size": 17733, "mtime": 1751120034997, "results": "25", "hashOfConfig": "18"}, {"size": 15916, "mtime": 1751119931275, "results": "26", "hashOfConfig": "18"}, {"size": 10342, "mtime": 1751289983317, "results": "27", "hashOfConfig": "18"}, {"size": 8656, "mtime": 1751121281468, "results": "28", "hashOfConfig": "18"}, {"size": 6035, "mtime": 1751121314460, "results": "29", "hashOfConfig": "18"}, {"size": 6808, "mtime": 1751121248928, "results": "30", "hashOfConfig": "18"}, {"size": 9524, "mtime": 1751289718752, "results": "31", "hashOfConfig": "18"}, {"size": 9028, "mtime": 1751290059629, "results": "32", "hashOfConfig": "18"}, {"size": 12114, "mtime": 1751294455192, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ncyc5o", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts", ["82", "83", "84", "85"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AdminBulkTurnins.tsx", ["86", "87"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\RakerDiverDashboard.tsx", ["88", "89"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileManager.tsx", [], ["90"], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileImportManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AvatarUpload.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\profileImport.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\PhotoMigrationManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\photoMigration.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\SourceManager.tsx", [], [], {"ruleId": "91", "severity": 1, "message": "92", "line": 306, "column": 15, "nodeType": "93", "messageId": "94", "endLine": 306, "endColumn": 19}, {"ruleId": "91", "severity": 1, "message": "92", "line": 320, "column": 15, "nodeType": "93", "messageId": "94", "endLine": 320, "endColumn": 19}, {"ruleId": "91", "severity": 1, "message": "92", "line": 398, "column": 15, "nodeType": "93", "messageId": "94", "endLine": 398, "endColumn": 19}, {"ruleId": "91", "severity": 1, "message": "92", "line": 439, "column": 15, "nodeType": "93", "messageId": "94", "endLine": 439, "endColumn": 19}, {"ruleId": "91", "severity": 1, "message": "95", "line": 73, "column": 15, "nodeType": "93", "messageId": "94", "endLine": 73, "endColumn": 22}, {"ruleId": "91", "severity": 1, "message": "92", "line": 99, "column": 15, "nodeType": "93", "messageId": "94", "endLine": 99, "endColumn": 19}, {"ruleId": "91", "severity": 1, "message": "92", "line": 81, "column": 15, "nodeType": "93", "messageId": "94", "endLine": 81, "endColumn": 19}, {"ruleId": "91", "severity": 1, "message": "95", "line": 120, "column": 15, "nodeType": "93", "messageId": "94", "endLine": 120, "endColumn": 22}, {"ruleId": "96", "severity": 1, "message": "97", "line": 30, "column": 6, "nodeType": "98", "endLine": 30, "endColumn": 14, "suggestions": "99", "suppressions": "100"}, "@typescript-eslint/no-unused-vars", "'data' is assigned a value but never used.", "Identifier", "unusedVar", "'success' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["101"], ["102"], {"desc": "103", "fix": "104"}, {"kind": "105", "justification": "106"}, "Update the dependencies array to be: [loadProfile, userId]", {"range": "107", "text": "108"}, "directive", "", [904, 912], "[loadProfile, userId]"]