{"ast": null, "code": "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextThursday} function options.\n */\n\n/**\n * @name nextThursday\n * @category Weekday Helpers\n * @summary When is the next Thursday?\n *\n * @description\n * When is the next Thursday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Thursday\n *\n * @example\n * // When is the next Thursday after Mar, 22, 2020?\n * const result = nextThursday(new Date(2020, 2, 22))\n * //=> Thur Mar 26 2020 00:00:00\n */\nexport function nextThursday(date, options) {\n  return nextDay(date, 4, options);\n}\n\n// Fallback for modularized imports:\nexport default nextThursday;", "map": {"version": 3, "names": ["nextDay", "nextThursday", "date", "options"], "sources": ["C:/Users/<USER>/node_modules/date-fns/nextThursday.js"], "sourcesContent": ["import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextThursday} function options.\n */\n\n/**\n * @name nextThursday\n * @category Weekday Helpers\n * @summary When is the next Thursday?\n *\n * @description\n * When is the next Thursday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Thursday\n *\n * @example\n * // When is the next Thursday after Mar, 22, 2020?\n * const result = nextThursday(new Date(2020, 2, 22))\n * //=> Thur Mar 26 2020 00:00:00\n */\nexport function nextThursday(date, options) {\n  return nextDay(date, 4, options);\n}\n\n// Fallback for modularized imports:\nexport default nextThursday;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC1C,OAAOH,OAAO,CAACE,IAAI,EAAE,CAAC,EAAEC,OAAO,CAAC;AAClC;;AAEA;AACA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}