{"ast": null, "code": "import { getDefaultOptions as getInternalDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * @name getDefaultOptions\n * @category Common Helpers\n * @summary Get default options.\n * @pure false\n *\n * @description\n * Returns an object that contains defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * You can change these with [setDefaultOptions](https://date-fns.org/docs/setDefaultOptions).\n *\n * @returns The default options\n *\n * @example\n * const result = getDefaultOptions()\n * //=> {}\n *\n * @example\n * setDefaultOptions({ weekStarsOn: 1, firstWeekContainsDate: 4 })\n * const result = getDefaultOptions()\n * //=> { weekStarsOn: 1, firstWeekContainsDate: 4 }\n */\nexport function getDefaultOptions() {\n  return Object.assign({}, getInternalDefaultOptions());\n}\n\n// Fallback for modularized imports:\nexport default getDefaultOptions;", "map": {"version": 3, "names": ["getDefaultOptions", "getInternalDefaultOptions", "Object", "assign"], "sources": ["C:/Users/<USER>/node_modules/date-fns/getDefaultOptions.js"], "sourcesContent": ["import { getDefaultOptions as getInternalDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * @name getDefaultOptions\n * @category Common Helpers\n * @summary Get default options.\n * @pure false\n *\n * @description\n * Returns an object that contains defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * You can change these with [setDefaultOptions](https://date-fns.org/docs/setDefaultOptions).\n *\n * @returns The default options\n *\n * @example\n * const result = getDefaultOptions()\n * //=> {}\n *\n * @example\n * setDefaultOptions({ weekStarsOn: 1, firstWeekContainsDate: 4 })\n * const result = getDefaultOptions()\n * //=> { weekStarsOn: 1, firstWeekContainsDate: 4 }\n */\nexport function getDefaultOptions() {\n  return Object.assign({}, getInternalDefaultOptions());\n}\n\n// Fallback for modularized imports:\nexport default getDefaultOptions;\n"], "mappings": "AAAA,SAASA,iBAAiB,IAAIC,yBAAyB,QAAQ,0BAA0B;;AAEzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASD,iBAAiBA,CAAA,EAAG;EAClC,OAAOE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,yBAAyB,CAAC,CAAC,CAAC;AACvD;;AAEA;AACA,eAAeD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}