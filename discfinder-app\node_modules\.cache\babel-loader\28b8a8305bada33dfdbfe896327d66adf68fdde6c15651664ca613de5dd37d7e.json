{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { isLeapYearIndex, parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class DayOfYearParser extends Parser {\n  priority = 90;\n  subpriority = 1;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"D\":\n      case \"DD\":\n        return parseNumericPattern(numericPatterns.dayOfYear, dateString);\n      case \"Do\":\n        return match.ordinalNumber(dateString, {\n          unit: \"date\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(date, value) {\n    const year = date.getFullYear();\n    const isLeapYear = isLeapYearIndex(year);\n    if (isLeapYear) {\n      return value >= 1 && value <= 366;\n    } else {\n      return value >= 1 && value <= 365;\n    }\n  }\n  set(date, _flags, value) {\n    date.setMonth(0, value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"Y\", \"R\", \"q\", \"Q\", \"M\", \"L\", \"w\", \"I\", \"d\", \"E\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["numericPatterns", "<PERSON><PERSON><PERSON>", "isLeapYearIndex", "parseNDigits", "parseNumericPattern", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "subpriority", "parse", "dateString", "token", "match", "dayOfYear", "ordinalNumber", "unit", "length", "validate", "date", "value", "year", "getFullYear", "isLeapYear", "set", "_flags", "setMonth", "setHours", "incompatibleTokens"], "sources": ["C:/Users/<USER>/node_modules/date-fns/parse/_lib/parsers/DayOfYearParser.js"], "sourcesContent": ["import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport {\n  isLeapYearIndex,\n  parseNDigits,\n  parseNumericPattern,\n} from \"../utils.js\";\n\nexport class DayOfYearParser extends Parser {\n  priority = 90;\n\n  subpriority = 1;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"D\":\n      case \"DD\":\n        return parseNumericPattern(numericPatterns.dayOfYear, dateString);\n      case \"Do\":\n        return match.ordinalNumber(dateString, { unit: \"date\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(date, value) {\n    const year = date.getFullYear();\n    const isLeapYear = isLeapYearIndex(year);\n    if (isLeapYear) {\n      return value >= 1 && value <= 366;\n    } else {\n      return value >= 1 && value <= 365;\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setMonth(0, value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"d\",\n    \"E\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iBAAiB;AACjD,SAASC,MAAM,QAAQ,cAAc;AAErC,SACEC,eAAe,EACfC,YAAY,EACZC,mBAAmB,QACd,aAAa;AAEpB,OAAO,MAAMC,eAAe,SAASJ,MAAM,CAAC;EAC1CK,QAAQ,GAAG,EAAE;EAEbC,WAAW,GAAG,CAAC;EAEfC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAON,mBAAmB,CAACJ,eAAe,CAACY,SAAS,EAAEH,UAAU,CAAC;MACnE,KAAK,IAAI;QACP,OAAOE,KAAK,CAACE,aAAa,CAACJ,UAAU,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAC,CAAC;MAC1D;QACE,OAAOX,YAAY,CAACO,KAAK,CAACK,MAAM,EAAEN,UAAU,CAAC;IACjD;EACF;EAEAO,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACpB,MAAMC,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC/B,MAAMC,UAAU,GAAGnB,eAAe,CAACiB,IAAI,CAAC;IACxC,IAAIE,UAAU,EAAE;MACd,OAAOH,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,GAAG;IACnC,CAAC,MAAM;MACL,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,GAAG;IACnC;EACF;EAEAI,GAAGA,CAACL,IAAI,EAAEM,MAAM,EAAEL,KAAK,EAAE;IACvBD,IAAI,CAACO,QAAQ,CAAC,CAAC,EAAEN,KAAK,CAAC;IACvBD,IAAI,CAACQ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB,OAAOR,IAAI;EACb;EAEAS,kBAAkB,GAAG,CACnB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}