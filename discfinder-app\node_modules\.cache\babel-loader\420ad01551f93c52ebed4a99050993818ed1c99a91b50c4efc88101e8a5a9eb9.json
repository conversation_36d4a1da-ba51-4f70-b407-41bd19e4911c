{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link roundToNearestMinutes} function options.\n */\n\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest minute\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * //=> Thu Jul 10 2014 12:15:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'floor' })\n * //=> Thu Jul 10 2014 12:12:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:12:34 to nearest half hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'ceil', nearestTo: 30 })\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function roundToNearestMinutes(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n  if (nearestTo < 1 || nearestTo > 30) return constructFrom(date, NaN);\n  const date_ = toDate(date, options?.in);\n  const fractionalSeconds = date_.getSeconds() / 60;\n  const fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60;\n  const minutes = date_.getMinutes() + fractionalSeconds + fractionalMilliseconds;\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n  const roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n  date_.setMinutes(roundedMinutes, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestMinutes;", "map": {"version": 3, "names": ["getRoundingMethod", "constructFrom", "toDate", "roundToNearestMinutes", "date", "options", "nearestTo", "NaN", "date_", "in", "fractionalSeconds", "getSeconds", "fractionalMilliseconds", "getMilliseconds", "minutes", "getMinutes", "method", "roundingMethod", "roundedMinutes", "setMinutes"], "sources": ["C:/Users/<USER>/node_modules/date-fns/roundToNearestMinutes.js"], "sourcesContent": ["import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link roundToNearestMinutes} function options.\n */\n\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest minute\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * //=> Thu Jul 10 2014 12:15:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'floor' })\n * //=> Thu Jul 10 2014 12:12:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:12:34 to nearest half hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'ceil', nearestTo: 30 })\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function roundToNearestMinutes(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n\n  if (nearestTo < 1 || nearestTo > 30) return constructFrom(date, NaN);\n\n  const date_ = toDate(date, options?.in);\n  const fractionalSeconds = date_.getSeconds() / 60;\n  const fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60;\n  const minutes =\n    date_.getMinutes() + fractionalSeconds + fractionalMilliseconds;\n\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n\n  const roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n\n  date_.setMinutes(roundedMinutes, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestMinutes;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACnD,MAAMC,SAAS,GAAGD,OAAO,EAAEC,SAAS,IAAI,CAAC;EAEzC,IAAIA,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,EAAE,EAAE,OAAOL,aAAa,CAACG,IAAI,EAAEG,GAAG,CAAC;EAEpE,MAAMC,KAAK,GAAGN,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEI,EAAE,CAAC;EACvC,MAAMC,iBAAiB,GAAGF,KAAK,CAACG,UAAU,CAAC,CAAC,GAAG,EAAE;EACjD,MAAMC,sBAAsB,GAAGJ,KAAK,CAACK,eAAe,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE;EAClE,MAAMC,OAAO,GACXN,KAAK,CAACO,UAAU,CAAC,CAAC,GAAGL,iBAAiB,GAAGE,sBAAsB;EAEjE,MAAMI,MAAM,GAAGX,OAAO,EAAEY,cAAc,IAAI,OAAO;EACjD,MAAMA,cAAc,GAAGjB,iBAAiB,CAACgB,MAAM,CAAC;EAEhD,MAAME,cAAc,GAAGD,cAAc,CAACH,OAAO,GAAGR,SAAS,CAAC,GAAGA,SAAS;EAEtEE,KAAK,CAACW,UAAU,CAACD,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,OAAOV,KAAK;AACd;;AAEA;AACA,eAAeL,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}