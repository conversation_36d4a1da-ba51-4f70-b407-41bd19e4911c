{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfMinute} function options.\n */\n\n/**\n * @name startOfMinute\n * @category Minute Helpers\n * @summary Return the start of a minute for the given date.\n *\n * @description\n * Return the start of a minute for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a minute\n *\n * @example\n * // The start of a minute for 1 December 2014 22:15:45.400:\n * const result = startOfMinute(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:00\n */\nexport function startOfMinute(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setSeconds(0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfMinute;", "map": {"version": 3, "names": ["toDate", "startOfMinute", "date", "options", "date_", "in", "setSeconds"], "sources": ["C:/Users/<USER>/node_modules/date-fns/startOfMinute.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfMinute} function options.\n */\n\n/**\n * @name startOfMinute\n * @category Minute Helpers\n * @summary Return the start of a minute for the given date.\n *\n * @description\n * Return the start of a minute for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a minute\n *\n * @example\n * // The start of a minute for 1 December 2014 22:15:45.400:\n * const result = startOfMinute(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:00\n */\nexport function startOfMinute(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setSeconds(0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfMinute;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvCD,KAAK,CAACE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EACtB,OAAOF,KAAK;AACd;;AAEA;AACA,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}