{"ast": null, "code": "export function buildFormatLongFn(args) {\n  return (options = {}) => {\n    // TODO: Remove String()\n    const width = options.width ? String(options.width) : args.defaultWidth;\n    const format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}", "map": {"version": 3, "names": ["buildFormatLongFn", "args", "options", "width", "String", "defaultWidth", "format", "formats"], "sources": ["C:/Users/<USER>/node_modules/date-fns/locale/_lib/buildFormatLongFn.js"], "sourcesContent": ["export function buildFormatLongFn(args) {\n  return (options = {}) => {\n    // TODO: Remove String()\n    const width = options.width ? String(options.width) : args.defaultWidth;\n    const format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n"], "mappings": "AAAA,OAAO,SAASA,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;IACvB;IACA,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,GAAGC,MAAM,CAACF,OAAO,CAACC,KAAK,CAAC,GAAGF,IAAI,CAACI,YAAY;IACvE,MAAMC,MAAM,GAAGL,IAAI,CAACM,OAAO,CAACJ,KAAK,CAAC,IAAIF,IAAI,CAACM,OAAO,CAACN,IAAI,CAACI,YAAY,CAAC;IACrE,OAAOC,MAAM;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}