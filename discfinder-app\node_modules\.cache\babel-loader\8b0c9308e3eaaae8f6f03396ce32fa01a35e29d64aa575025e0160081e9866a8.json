{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link differenceInCalendarYears} function options.\n */\n\n/**\n * @name differenceInCalendarYears\n * @category Year Helpers\n * @summary Get the number of calendar years between the given dates.\n *\n * @description\n * Get the number of calendar years between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n\n * @returns The number of calendar years\n *\n * @example\n * // How many calendar years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInCalendarYears(\n *   new Date(2015, 1, 11),\n *   new Date(2013, 11, 31)\n * );\n * //=> 2\n */\nexport function differenceInCalendarYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return laterDate_.getFullYear() - earlierDate_.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarYears;", "map": {"version": 3, "names": ["normalizeDates", "differenceInCalendarYears", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "getFullYear"], "sources": ["C:/Users/<USER>/node_modules/date-fns/differenceInCalendarYears.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link differenceInCalendarYears} function options.\n */\n\n/**\n * @name differenceInCalendarYears\n * @category Year Helpers\n * @summary Get the number of calendar years between the given dates.\n *\n * @description\n * Get the number of calendar years between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n\n * @returns The number of calendar years\n *\n * @example\n * // How many calendar years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInCalendarYears(\n *   new Date(2015, 1, 11),\n *   new Date(2013, 11, 31)\n * );\n * //=> 2\n */\nexport function differenceInCalendarYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return laterDate_.getFullYear() - earlierDate_.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarYears;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;;AAEzD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACzE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGN,cAAc,CAC/CI,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EACD,OAAOE,UAAU,CAACG,WAAW,CAAC,CAAC,GAAGF,YAAY,CAACE,WAAW,CAAC,CAAC;AAC9D;;AAEA;AACA,eAAeP,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}