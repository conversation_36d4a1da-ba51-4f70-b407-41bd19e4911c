{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getHours} function options.\n */\n\n/**\n * @name getHours\n * @category Hour Helpers\n * @summary Get the hours of the given date.\n *\n * @description\n * Get the hours of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The hours\n *\n * @example\n * // Get the hours of 29 February 2012 11:45:00:\n * const result = getHours(new Date(2012, 1, 29, 11, 45))\n * //=> 11\n */\nexport function getHours(date, options) {\n  return toDate(date, options?.in).getHours();\n}\n\n// Fallback for modularized imports:\nexport default getHours;", "map": {"version": 3, "names": ["toDate", "getHours", "date", "options", "in"], "sources": ["C:/Users/<USER>/node_modules/date-fns/getHours.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getHours} function options.\n */\n\n/**\n * @name getHours\n * @category Hour Helpers\n * @summary Get the hours of the given date.\n *\n * @description\n * Get the hours of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The hours\n *\n * @example\n * // Get the hours of 29 February 2012 11:45:00:\n * const result = getHours(new Date(2012, 1, 29, 11, 45))\n * //=> 11\n */\nexport function getHours(date, options) {\n  return toDate(date, options?.in).getHours();\n}\n\n// Fallback for modularized imports:\nexport default getHours;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACtC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACH,QAAQ,CAAC,CAAC;AAC7C;;AAEA;AACA,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}