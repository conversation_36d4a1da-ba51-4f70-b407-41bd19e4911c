{"ast": null, "code": "const dateLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"P\":\n      return formatLong.date({\n        width: \"short\"\n      });\n    case \"PP\":\n      return formatLong.date({\n        width: \"medium\"\n      });\n    case \"PPP\":\n      return formatLong.date({\n        width: \"long\"\n      });\n    case \"PPPP\":\n    default:\n      return formatLong.date({\n        width: \"full\"\n      });\n  }\n};\nconst timeLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"p\":\n      return formatLong.time({\n        width: \"short\"\n      });\n    case \"pp\":\n      return formatLong.time({\n        width: \"medium\"\n      });\n    case \"ppp\":\n      return formatLong.time({\n        width: \"long\"\n      });\n    case \"pppp\":\n    default:\n      return formatLong.time({\n        width: \"full\"\n      });\n  }\n};\nconst dateTimeLongFormatter = (pattern, formatLong) => {\n  const matchResult = pattern.match(/(P+)(p+)?/) || [];\n  const datePattern = matchResult[1];\n  const timePattern = matchResult[2];\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n  let dateTimeFormat;\n  switch (datePattern) {\n    case \"P\":\n      dateTimeFormat = formatLong.dateTime({\n        width: \"short\"\n      });\n      break;\n    case \"PP\":\n      dateTimeFormat = formatLong.dateTime({\n        width: \"medium\"\n      });\n      break;\n    case \"PPP\":\n      dateTimeFormat = formatLong.dateTime({\n        width: \"long\"\n      });\n      break;\n    case \"PPPP\":\n    default:\n      dateTimeFormat = formatLong.dateTime({\n        width: \"full\"\n      });\n      break;\n  }\n  return dateTimeFormat.replace(\"{{date}}\", dateLongFormatter(datePattern, formatLong)).replace(\"{{time}}\", timeLongFormatter(timePattern, formatLong));\n};\nexport const longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pattern", "formatLong", "date", "width", "time<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "time", "dateTimeLongFormatter", "matchResult", "match", "datePattern", "timePattern", "dateTimeFormat", "dateTime", "replace", "longFormatters", "p", "P"], "sources": ["C:/Users/<USER>/node_modules/date-fns/_lib/format/longFormatters.js"], "sourcesContent": ["const dateLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"P\":\n      return formatLong.date({ width: \"short\" });\n    case \"PP\":\n      return formatLong.date({ width: \"medium\" });\n    case \"PPP\":\n      return formatLong.date({ width: \"long\" });\n    case \"PPPP\":\n    default:\n      return formatLong.date({ width: \"full\" });\n  }\n};\n\nconst timeLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"p\":\n      return formatLong.time({ width: \"short\" });\n    case \"pp\":\n      return formatLong.time({ width: \"medium\" });\n    case \"ppp\":\n      return formatLong.time({ width: \"long\" });\n    case \"pppp\":\n    default:\n      return formatLong.time({ width: \"full\" });\n  }\n};\n\nconst dateTimeLongFormatter = (pattern, formatLong) => {\n  const matchResult = pattern.match(/(P+)(p+)?/) || [];\n  const datePattern = matchResult[1];\n  const timePattern = matchResult[2];\n\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n\n  let dateTimeFormat;\n\n  switch (datePattern) {\n    case \"P\":\n      dateTimeFormat = formatLong.dateTime({ width: \"short\" });\n      break;\n    case \"PP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"medium\" });\n      break;\n    case \"PPP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"long\" });\n      break;\n    case \"PPPP\":\n    default:\n      dateTimeFormat = formatLong.dateTime({ width: \"full\" });\n      break;\n  }\n\n  return dateTimeFormat\n    .replace(\"{{date}}\", dateLongFormatter(datePattern, formatLong))\n    .replace(\"{{time}}\", timeLongFormatter(timePattern, formatLong));\n};\n\nexport const longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter,\n};\n"], "mappings": "AAAA,MAAMA,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,UAAU,KAAK;EACjD,QAAQD,OAAO;IACb,KAAK,GAAG;MACN,OAAOC,UAAU,CAACC,IAAI,CAAC;QAAEC,KAAK,EAAE;MAAQ,CAAC,CAAC;IAC5C,KAAK,IAAI;MACP,OAAOF,UAAU,CAACC,IAAI,CAAC;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;IAC7C,KAAK,KAAK;MACR,OAAOF,UAAU,CAACC,IAAI,CAAC;QAAEC,KAAK,EAAE;MAAO,CAAC,CAAC;IAC3C,KAAK,MAAM;IACX;MACE,OAAOF,UAAU,CAACC,IAAI,CAAC;QAAEC,KAAK,EAAE;MAAO,CAAC,CAAC;EAC7C;AACF,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAACJ,OAAO,EAAEC,UAAU,KAAK;EACjD,QAAQD,OAAO;IACb,KAAK,GAAG;MACN,OAAOC,UAAU,CAACI,IAAI,CAAC;QAAEF,KAAK,EAAE;MAAQ,CAAC,CAAC;IAC5C,KAAK,IAAI;MACP,OAAOF,UAAU,CAACI,IAAI,CAAC;QAAEF,KAAK,EAAE;MAAS,CAAC,CAAC;IAC7C,KAAK,KAAK;MACR,OAAOF,UAAU,CAACI,IAAI,CAAC;QAAEF,KAAK,EAAE;MAAO,CAAC,CAAC;IAC3C,KAAK,MAAM;IACX;MACE,OAAOF,UAAU,CAACI,IAAI,CAAC;QAAEF,KAAK,EAAE;MAAO,CAAC,CAAC;EAC7C;AACF,CAAC;AAED,MAAMG,qBAAqB,GAAGA,CAACN,OAAO,EAAEC,UAAU,KAAK;EACrD,MAAMM,WAAW,GAAGP,OAAO,CAACQ,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE;EACpD,MAAMC,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAClC,MAAMG,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;EAElC,IAAI,CAACG,WAAW,EAAE;IAChB,OAAOX,iBAAiB,CAACC,OAAO,EAAEC,UAAU,CAAC;EAC/C;EAEA,IAAIU,cAAc;EAElB,QAAQF,WAAW;IACjB,KAAK,GAAG;MACNE,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QAAET,KAAK,EAAE;MAAQ,CAAC,CAAC;MACxD;IACF,KAAK,IAAI;MACPQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QAAET,KAAK,EAAE;MAAS,CAAC,CAAC;MACzD;IACF,KAAK,KAAK;MACRQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QAAET,KAAK,EAAE;MAAO,CAAC,CAAC;MACvD;IACF,KAAK,MAAM;IACX;MACEQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QAAET,KAAK,EAAE;MAAO,CAAC,CAAC;MACvD;EACJ;EAEA,OAAOQ,cAAc,CAClBE,OAAO,CAAC,UAAU,EAAEd,iBAAiB,CAACU,WAAW,EAAER,UAAU,CAAC,CAAC,CAC/DY,OAAO,CAAC,UAAU,EAAET,iBAAiB,CAACM,WAAW,EAAET,UAAU,CAAC,CAAC;AACpE,CAAC;AAED,OAAO,MAAMa,cAAc,GAAG;EAC5BC,CAAC,EAAEX,iBAAiB;EACpBY,CAAC,EAAEV;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}