{"ast": null, "code": "import { addMinutes } from \"./addMinutes.js\";\n\n/**\n * The {@link subMinutes} function options.\n */\n\n/**\n * @name subMinutes\n * @category Minute Helpers\n * @summary Subtract the specified number of minutes from the given date.\n *\n * @description\n * Subtract the specified number of minutes from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the minutes subtracted\n *\n * @example\n * // Subtract 30 minutes from 10 July 2014 12:00:00:\n * const result = subMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 11:30:00\n */\nexport function subMinutes(date, amount, options) {\n  return addMinutes(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMinutes;", "map": {"version": 3, "names": ["addMinutes", "subMinutes", "date", "amount", "options"], "sources": ["C:/Users/<USER>/node_modules/date-fns/subMinutes.js"], "sourcesContent": ["import { addMinutes } from \"./addMinutes.js\";\n\n/**\n * The {@link subMinutes} function options.\n */\n\n/**\n * @name subMinutes\n * @category Minute Helpers\n * @summary Subtract the specified number of minutes from the given date.\n *\n * @description\n * Subtract the specified number of minutes from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the minutes subtracted\n *\n * @example\n * // Subtract 30 minutes from 10 July 2014 12:00:00:\n * const result = subMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 11:30:00\n */\nexport function subMinutes(date, amount, options) {\n  return addMinutes(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMinutes;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAChD,OAAOJ,UAAU,CAACE,IAAI,EAAE,CAACC,MAAM,EAAEC,OAAO,CAAC;AAC3C;;AAEA;AACA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}