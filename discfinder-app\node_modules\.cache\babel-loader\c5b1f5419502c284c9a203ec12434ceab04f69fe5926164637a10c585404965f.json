{"ast": null, "code": "import { addMilliseconds } from \"./addMilliseconds.js\";\nimport { millisecondsInHour } from \"./constants.js\";\n\n/**\n * The {@link addHours} function options.\n */\n\n/**\n * @name addHours\n * @category Hour Helpers\n * @summary Add the specified number of hours to the given date.\n *\n * @description\n * Add the specified number of hours to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of hours to be added\n * @param options - An object with options\n *\n * @returns The new date with the hours added\n *\n * @example\n * // Add 2 hours to 10 July 2014 23:00:00:\n * const result = addHours(new Date(2014, 6, 10, 23, 0), 2)\n * //=> Fri Jul 11 2014 01:00:00\n */\nexport function addHours(date, amount, options) {\n  return addMilliseconds(date, amount * millisecondsInHour, options);\n}\n\n// Fallback for modularized imports:\nexport default addHours;", "map": {"version": 3, "names": ["addMilliseconds", "millisecondsInHour", "addHours", "date", "amount", "options"], "sources": ["C:/Users/<USER>/node_modules/date-fns/addHours.js"], "sourcesContent": ["import { addMilliseconds } from \"./addMilliseconds.js\";\nimport { millisecondsInHour } from \"./constants.js\";\n\n/**\n * The {@link addHours} function options.\n */\n\n/**\n * @name addHours\n * @category Hour Helpers\n * @summary Add the specified number of hours to the given date.\n *\n * @description\n * Add the specified number of hours to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of hours to be added\n * @param options - An object with options\n *\n * @returns The new date with the hours added\n *\n * @example\n * // Add 2 hours to 10 July 2014 23:00:00:\n * const result = addHours(new Date(2014, 6, 10, 23, 0), 2)\n * //=> Fri Jul 11 2014 01:00:00\n */\nexport function addHours(date, amount, options) {\n  return addMilliseconds(date, amount * millisecondsInHour, options);\n}\n\n// Fallback for modularized imports:\nexport default addHours;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sBAAsB;AACtD,SAASC,kBAAkB,QAAQ,gBAAgB;;AAEnD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAOL,eAAe,CAACG,IAAI,EAAEC,MAAM,GAAGH,kBAAkB,EAAEI,OAAO,CAAC;AACpE;;AAEA;AACA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}