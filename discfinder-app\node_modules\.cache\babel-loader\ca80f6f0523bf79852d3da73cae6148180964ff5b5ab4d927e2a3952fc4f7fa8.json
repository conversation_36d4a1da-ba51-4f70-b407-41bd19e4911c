{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isAfter\n * @category Common Helpers\n * @summary Is the first date after the second one?\n *\n * @description\n * Is the first date after the second one?\n *\n * @param date - The date that should be after the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is after the second date\n *\n * @example\n * // Is 10 July 1989 after 11 February 1987?\n * const result = isAfter(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> true\n */\nexport function isAfter(date, dateToCompare) {\n  return +toDate(date) > +toDate(dateToCompare);\n}\n\n// Fallback for modularized imports:\nexport default isAfter;", "map": {"version": 3, "names": ["toDate", "isAfter", "date", "dateToCompare"], "sources": ["C:/Users/<USER>/node_modules/date-fns/isAfter.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name isAfter\n * @category Common Helpers\n * @summary Is the first date after the second one?\n *\n * @description\n * Is the first date after the second one?\n *\n * @param date - The date that should be after the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is after the second date\n *\n * @example\n * // Is 10 July 1989 after 11 February 1987?\n * const result = isAfter(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> true\n */\nexport function isAfter(date, dateToCompare) {\n  return +toDate(date) > +toDate(dateToCompare);\n}\n\n// Fallback for modularized imports:\nexport default isAfter;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAEC,aAAa,EAAE;EAC3C,OAAO,CAACH,MAAM,CAACE,IAAI,CAAC,GAAG,CAACF,MAAM,CAACG,aAAa,CAAC;AAC/C;;AAEA;AACA,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}