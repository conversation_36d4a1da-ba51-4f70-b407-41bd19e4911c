{"ast": null, "code": "import { addDays } from \"./addDays.js\";\nimport { getDay } from \"./getDay.js\";\n\n/**\n * The {@link nextDay} function options.\n */\n\n/**\n * @name nextDay\n * @category Weekday Helpers\n * @summary When is the next day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to check\n * @param day - Day of the week\n * @param options - An object with options\n *\n * @returns The date is the next day of the week\n *\n * @example\n * // When is the next Monday after Mar, 20, 2020?\n * const result = nextDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 23 2020 00:00:00\n *\n * @example\n * // When is the next Tuesday after Mar, 21, 2020?\n * const result = nextDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 24 2020 00:00:00\n */\nexport function nextDay(date, day, options) {\n  let delta = day - getDay(date, options);\n  if (delta <= 0) delta += 7;\n  return addDays(date, delta, options);\n}\n\n// Fallback for modularized imports:\nexport default nextDay;", "map": {"version": 3, "names": ["addDays", "getDay", "nextDay", "date", "day", "options", "delta"], "sources": ["C:/Users/<USER>/node_modules/date-fns/nextDay.js"], "sourcesContent": ["import { addDays } from \"./addDays.js\";\nimport { getDay } from \"./getDay.js\";\n\n/**\n * The {@link nextDay} function options.\n */\n\n/**\n * @name nextDay\n * @category Weekday Helpers\n * @summary When is the next day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to check\n * @param day - Day of the week\n * @param options - An object with options\n *\n * @returns The date is the next day of the week\n *\n * @example\n * // When is the next Monday after Mar, 20, 2020?\n * const result = nextDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 23 2020 00:00:00\n *\n * @example\n * // When is the next Tuesday after Mar, 21, 2020?\n * const result = nextDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 24 2020 00:00:00\n */\nexport function nextDay(date, day, options) {\n  let delta = day - getDay(date, options);\n  if (delta <= 0) delta += 7;\n\n  return addDays(date, delta, options);\n}\n\n// Fallback for modularized imports:\nexport default nextDay;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC1C,IAAIC,KAAK,GAAGF,GAAG,GAAGH,MAAM,CAACE,IAAI,EAAEE,OAAO,CAAC;EACvC,IAAIC,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC;EAE1B,OAAON,OAAO,CAACG,IAAI,EAAEG,KAAK,EAAED,OAAO,CAAC;AACtC;;AAEA;AACA,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}