{"ast": null, "code": "import { secondsInMinute } from \"./constants.js\";\n\n/**\n * @name minutesToSeconds\n * @category Conversion Helpers\n * @summary Convert minutes to seconds.\n *\n * @description\n * Convert a number of minutes to a full number of seconds.\n *\n * @param minutes - The number of minutes to be converted\n *\n * @returns The number of minutes converted in seconds\n *\n * @example\n * // Convert 2 minutes to seconds\n * const result = minutesToSeconds(2)\n * //=> 120\n */\nexport function minutesToSeconds(minutes) {\n  return Math.trunc(minutes * secondsInMinute);\n}\n\n// Fallback for modularized imports:\nexport default minutesToSeconds;", "map": {"version": 3, "names": ["secondsInMinute", "minutesToSeconds", "minutes", "Math", "trunc"], "sources": ["C:/Users/<USER>/node_modules/date-fns/minutesToSeconds.js"], "sourcesContent": ["import { secondsInMinute } from \"./constants.js\";\n\n/**\n * @name minutesToSeconds\n * @category Conversion Helpers\n * @summary Convert minutes to seconds.\n *\n * @description\n * Convert a number of minutes to a full number of seconds.\n *\n * @param minutes - The number of minutes to be converted\n *\n * @returns The number of minutes converted in seconds\n *\n * @example\n * // Convert 2 minutes to seconds\n * const result = minutesToSeconds(2)\n * //=> 120\n */\nexport function minutesToSeconds(minutes) {\n  return Math.trunc(minutes * secondsInMinute);\n}\n\n// Fallback for modularized imports:\nexport default minutesToSeconds;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gBAAgB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EACxC,OAAOC,IAAI,CAACC,KAAK,CAACF,OAAO,GAAGF,eAAe,CAAC;AAC9C;;AAEA;AACA,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}