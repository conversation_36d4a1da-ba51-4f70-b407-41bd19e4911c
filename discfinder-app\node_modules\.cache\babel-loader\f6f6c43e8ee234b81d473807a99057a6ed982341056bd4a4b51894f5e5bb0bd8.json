{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { differenceInDays } from \"./differenceInDays.js\";\n\n/**\n * The {@link differenceInWeeks} function options.\n */\n\n/**\n * @name differenceInWeeks\n * @category Week Helpers\n * @summary Get the number of full weeks between the given dates.\n *\n * @description\n * Get the number of full weeks between two dates. Fractional weeks are\n * truncated towards zero by default.\n *\n * One \"full week\" is the distance between a local time in one day to the same\n * local time 7 days earlier or later. A full week can sometimes be less than\n * or more than 7*24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 7*24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/(7*24))|0`.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full weeks\n *\n * @example\n * // How many full weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInWeeks(new Date(2014, 6, 20), new Date(2014, 6, 5))\n * //=> 2\n *\n * @example\n * // How many full weeks are between\n * // 1 March 2020 0:00 and 6 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 8 weeks (54 days),\n * // even if DST starts and the period has\n * // only 54*24-1 hours.\n * const result = differenceInWeeks(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 6)\n * )\n * //=> 8\n */\nexport function differenceInWeeks(laterDate, earlierDate, options) {\n  const diff = differenceInDays(laterDate, earlierDate, options) / 7;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInWeeks;", "map": {"version": 3, "names": ["getRoundingMethod", "differenceInDays", "differenceInWeeks", "laterDate", "earlierDate", "options", "diff", "roundingMethod"], "sources": ["C:/Users/<USER>/node_modules/date-fns/differenceInWeeks.js"], "sourcesContent": ["import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { differenceInDays } from \"./differenceInDays.js\";\n\n/**\n * The {@link differenceInWeeks} function options.\n */\n\n/**\n * @name differenceInWeeks\n * @category Week Helpers\n * @summary Get the number of full weeks between the given dates.\n *\n * @description\n * Get the number of full weeks between two dates. Fractional weeks are\n * truncated towards zero by default.\n *\n * One \"full week\" is the distance between a local time in one day to the same\n * local time 7 days earlier or later. A full week can sometimes be less than\n * or more than 7*24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 7*24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/(7*24))|0`.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full weeks\n *\n * @example\n * // How many full weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInWeeks(new Date(2014, 6, 20), new Date(2014, 6, 5))\n * //=> 2\n *\n * @example\n * // How many full weeks are between\n * // 1 March 2020 0:00 and 6 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 8 weeks (54 days),\n * // even if DST starts and the period has\n * // only 54*24-1 hours.\n * const result = differenceInWeeks(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 6)\n * )\n * //=> 8\n */\nexport function differenceInWeeks(laterDate, earlierDate, options) {\n  const diff = differenceInDays(laterDate, earlierDate, options) / 7;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInWeeks;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,gBAAgB,QAAQ,uBAAuB;;AAExD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACjE,MAAMC,IAAI,GAAGL,gBAAgB,CAACE,SAAS,EAAEC,WAAW,EAAEC,OAAO,CAAC,GAAG,CAAC;EAClE,OAAOL,iBAAiB,CAACK,OAAO,EAAEE,cAAc,CAAC,CAACD,IAAI,CAAC;AACzD;;AAEA;AACA,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}