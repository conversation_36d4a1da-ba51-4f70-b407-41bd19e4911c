{"name": "discfinder-app", "version": "0.1.0", "private": true, "dependencies": {"@glideapps/tables": "^1.0.21", "@supabase/supabase-js": "^2.50.2", "axios": "^1.6.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "import-profiles": "node import-glide-profiles.js", "import-csv": "node import-csv-profiles.js", "test-import": "node test-profile-import.js", "migrate-photos": "node migrate-photos.js profiles", "migrate-disc-photos": "node migrate-photos.js discs", "migrate-sources": "node run-sources-migration.js", "test-sources": "node test-sources.js", "import-sources": "node import-sources.js", "update-sources-table": "node import-sources.js --update-table", "test-sources-import": "node test-sources-import.js", "sources-status": "node test-sources-import.js --status", "validate-found-discs": "node validate-found-disc-import.js", "import-found-discs": "node import-found-discs.js", "test-found-disc-import": "node import-found-discs.js --test", "migrate-disc-images": "node migrate-found-disc-images.js", "test-image-migration": "node migrate-found-disc-images.js --test", "image-migration-status": "node migrate-found-disc-images.js --status", "import-contact-attempts": "node import-contact-attempts.js", "test-contact-import": "node import-contact-attempts.js --test", "contact-attempts-status": "node import-contact-attempts.js --status", "test-full-import": "node test-found-disc-import.js", "cleanup-found-discs": "node cleanup-found-discs.js", "cleanup-found-discs-dry-run": "node cleanup-found-discs.js --dry-run", "cleanup-stats": "node cleanup-found-discs.js --stats", "check-image-migration": "node check-image-migration-status.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}