import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Navbar: React.FC = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  return (
    <nav className="bg-white shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <Link to="/" className="text-2xl font-bold text-primary-600">
            DiscFinder
          </Link>
          
          <div className="hidden md:flex space-x-6">
            <Link 
              to="/report-found" 
              className="text-gray-700 hover:text-primary-600 transition-colors"
            >
              Report Found Disc
            </Link>
            <Link 
              to="/search-lost" 
              className="text-gray-700 hover:text-primary-600 transition-colors"
            >
              Search Lost Discs
            </Link>
            {user && (
              <Link 
                to="/my-reports" 
                className="text-gray-700 hover:text-primary-600 transition-colors"
              >
                My Reports
              </Link>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {user ? (
              <div className="flex items-center space-x-4">
                <span className="text-gray-700">
                  Welcome, {user.email}
                </span>
                <button
                  onClick={handleSignOut}
                  className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors"
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  to="/login"
                  className="text-primary-600 hover:text-primary-700 transition-colors"
                >
                  Sign In
                </Link>
                <Link
                  to="/register"
                  className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Mobile menu */}
        <div className="md:hidden pb-4">
          <div className="flex flex-col space-y-2">
            <Link 
              to="/report-found" 
              className="text-gray-700 hover:text-primary-600 transition-colors py-2"
            >
              Report Found Disc
            </Link>
            <Link 
              to="/search-lost" 
              className="text-gray-700 hover:text-primary-600 transition-colors py-2"
            >
              Search Lost Discs
            </Link>
            {user && (
              <Link 
                to="/my-reports" 
                className="text-gray-700 hover:text-primary-600 transition-colors py-2"
              >
                My Reports
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
