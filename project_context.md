# DiscFinder Project Context

**Last Updated:** 2025-06-28  
**Version:** 0.1.0  
**Status:** Active Development

## 🎯 Project Overview

DiscFinder is a lost and found application specifically designed for disc golf players. It helps reunite disc golfers with their lost discs through a community-driven database where users can report found discs and search for their lost ones.

## 🏗️ Architecture

### Frontend
- **Framework:** React 19.1.0 with TypeScript
- **Styling:** Tailwind CSS + Custom CSS
- **Build Tool:** Create React App 5.0.1
- **Location:** `discfinder-app/` (main app)

### Backend
- **Database:** Supabase (PostgreSQL)
- **Authentication:** Supabase Auth with JWT
- **Storage:** Supabase Storage for disc images
- **Real-time:** Supabase Realtime (configured)

### Key Technologies
- TypeScript 4.9.5
- Supabase JS Client
- Row Level Security (RLS)
- Image upload with drag & drop

## 👥 User Roles & Permissions

### 🔓 Guest (Not Signed In)
- ✅ Search for discs
- ✅ See basic disc info (brand, mold, color, location, date)
- ❌ Cannot see contact details, names on discs, or descriptions
- ❌ Cannot report found discs

### 🔑 User (Authenticated)
- ✅ All guest capabilities
- ✅ See full disc details including contact information
- ✅ Report found discs with 1-2 images
- ✅ Manage their own reports

### 👑 Admin (Project Owner)
- ✅ All user capabilities
- ✅ Manage all disc reports
- ✅ Set return status for found discs
- ✅ Access admin dashboard
- ✅ Manage bulk turn-ins

### 🏊 RakerDiver (Special Role)
- ✅ All user capabilities
- ✅ Record bulk disc turn-ins from water hazards
- ✅ Track collection and delivery details
- ✅ View payment tracking

## 🗄️ Database Schema

### Core Tables
- **`profiles`** - User profiles extending auth.users with import support
- **`found_discs`** - Reported found discs with images and source references
- **`sources`** - Predefined locations/events where discs are found (admin managed)
- **`lost_discs`** - Reported lost discs (planned)
- **`disc_matches`** - Automatic matching system (planned)
- **`contact_attempts`** - Communication tracking with disc owners (admin-only access)

### Profile Import Tables
- **`imported_profiles_staging`** - Staging table for imported profiles awaiting signup
- **`imported_profiles`** - View of imported profiles needing signup
- **Import Functions** - `import_legacy_profile()` (profile linking handled in JavaScript)

### RakerDiver Tables
- **`bulk_turnins`** - Bulk disc collection records
- **`bulk_turnin_payments`** - Payment tracking

### Key Features
- **Return Status:** Found, Returned to Owner, Donated, Sold, Trashed
- **Image Storage:** Up to 2 images per disc in Supabase Storage
- **Role-based Views:** `public_found_discs`, `admin_found_discs`
- **Source Management:** Admin-controlled dropdown of predefined locations/events
- **Two-tier Location System:** General source + specific location details
- **Contact Tracking:** Admin-only communication log with disc owners

## 📍 Sources System

### Overview
The Sources system provides a two-tier location structure for reporting found discs:
1. **Source** - General location/event (admin-managed dropdown)
2. **Specific Location** - Detailed location within the source (free text)

### Admin Management
- **Create Sources** - Add new predefined locations/events
- **Edit Sources** - Update name, description, sort order
- **Active/Inactive** - Control visibility in dropdown
- **Sort Order** - Custom ordering in dropdown list

### User Experience
- **Required Selection** - Users must choose a source when reporting found discs
- **"Other" Option** - Always available fallback option
- **Specific Details** - Additional field for precise location within source

### Database Structure
- **`sources` table** - Stores predefined locations with admin controls and legacy import mapping
- **`found_discs.source_id`** - References the selected source
- **`found_discs.location_found`** - Defaults to "Exact location unknown." if not specified
- **`sources.legacy_row_id`** - Maps to external system row IDs for import tracking
- **Row Level Security** - Protects admin-only operations
- **Views Updated** - Both public and admin views include source information

### Examples of Sources
- Tournament events (e.g., "2022 DDO", "2022 Worlds")
- Local courses (e.g., "Jones, Emporia", "Lawrence, KS (Local)")
- Special locations (e.g., "Emporia, KS Ponds", "Centennial Birdie Bin")
- Unknown/Other for unspecified locations

## 🚀 Current Features (Working)

### ✅ Implemented & Tested
- **Authentication System** - Complete with role-based access and profile linking
- **Report Found Disc** - Full form with image upload (1-2 photos) and source selection
- **Search Functionality** - Role-based filtering of results with source information
- **Image Upload** - Drag & drop, validation, Supabase Storage
- **Admin Dashboard** - Return status management and source management
- **RakerDiver System** - Bulk turn-in tracking and payment management
- **Return Status Tracking** - Admin can mark discs as returned/donated/etc.
- **Source Management** - Admin-controlled dropdown of disc found locations/events
- **Profile Import System** - ✅ COMPLETE: 143 profiles imported from Glide app
- **Enhanced Profiles** - PDGA numbers, social media, avatar upload
- **Profile Management** - Complete profile editing with new fields
- **Automatic Profile Linking** - Imported users get their data when they sign up
- **Contact Tracking System** - Admin-only communication log for tracking contact attempts with disc owners

### 🔧 Technical Implementation
- **Row Level Security** - Protects sensitive data by user role
- **Public Views** - Automatically filter data based on authentication
- **Image Validation** - 10MB per image, JPEG/PNG/WebP support
- **Responsive Design** - Works on mobile and desktop

## 📁 Project Structure

```
discfinder-app/
├── src/
│   ├── components/          # React components
│   │   ├── ImageUpload.tsx  # Image upload with drag & drop
│   │   ├── ReturnStatusManager.tsx
│   │   ├── RakerDiverDashboard.tsx
│   │   ├── AdminBulkTurnins.tsx
│   │   ├── SourceManager.tsx        # Admin source management interface
│   │   ├── ProfileImportManager.tsx  # Profile import interface
│   │   ├── ProfileManager.tsx        # Enhanced profile editing
│   │   └── AvatarUpload.tsx         # Avatar/photo upload
│   ├── contexts/
│   │   └── AuthContext.tsx  # Authentication state management
│   ├── lib/
│   │   ├── supabase.ts      # Supabase client & services
│   │   └── profileImport.ts # Profile import functionality
│   └── App.tsx              # Main application component
├── public/                  # Static assets
├── *.sql                    # Database migration files
├── *.md                     # Setup and documentation files
├── import-glide-profiles.js # Profile import script
├── test-profile-import.js   # Import testing script
└── package.json
```

## 🔧 Environment Setup

### Required Environment Variables
```bash
# .env.local
REACT_APP_SUPABASE_URL=https://your-project.supabase.co
REACT_APP_SUPABASE_ANON_KEY=your-anon-key
```

### Development Commands
```bash
npm start              # Start development server (localhost:3000)
npm test               # Run tests
npm run build          # Build for production
npm run import-profiles # Import profiles from Glide app (API-based)
npm run import-csv     # Import profiles from CSV export (COMPLETED)
npm run test-import    # Test profile import functionality
npm run import-sources # Import sources from external_data/sources.csv
npm run sources-status # Show current sources status and counts
```

## 📋 Setup Checklist

### ✅ Completed Setup
- [x] Supabase project created and configured
- [x] Database schema deployed
- [x] Authentication system implemented
- [x] Image storage configured
- [x] RakerDiver role and bulk turn-ins
- [x] Return status management
- [x] Admin functionality
- [x] Profile import system from Glide app
- [x] Enhanced profile management with new fields
- [x] Avatar upload and management system

### 🔄 Database Migrations Applied
- [x] `20250612051815_create_found_discs_schema.sql` - Core schema
- [x] `AUTH_SETUP.sql` - Authentication and roles
- [x] `SUPABASE_STORAGE_SETUP.sql` - Image storage
- [x] `ADD_RAKERDIVER_ROLE.sql` - RakerDiver functionality
- [x] `ADD_RETURN_STATUS.sql` - Return status tracking
- [x] `UPDATE_MODEL_TO_MOLD.sql` - Terminology update
- [x] `ADD_PROFILE_IMPORT_FIELDS.sql` - Profile import and avatar support
- [x] `ADD_SOURCES_TABLE.sql` - Sources table for predefined disc found locations
- [x] `UPDATE_SOURCES_FOR_IMPORT.sql` - Added legacy_row_id and location_found default
- [x] `ADD_CONTACT_ATTEMPTS.sql` - Contact tracking system for admin communication with disc owners

## 🎨 Design Preferences

### User Preferences (from memories)
- Use 'Mold' instead of 'Model' for disc terminology
- Avoid dropdowns for Disc Type and Condition (use example text)
- Make Brand, Mold, and Color required fields
- Consistent styling across all components
- Support 1-2 pictures when reporting found discs

## 🚧 Known Issues & Solutions

### Authentication
- RLS policies properly configured for role-based access
- Demo mode fallback when Supabase not configured
- Automatic role assignment based on email

### Image Upload
- 50MB bucket limit, 10MB per image
- Automatic cleanup of orphaned images after 24 hours
- Public read access for search results

## 🎯 Next Steps & Roadmap

### Immediate Priorities
1. **Lost Disc Reporting** - Implement the lost disc form
2. **Automatic Matching** - Build UI for disc matching system
3. **Contact System UI** - Build admin interface for logging contact attempts
4. **Email Notifications** - Notify users of matches

### Future Enhancements
- Advanced search filters
- Mobile app development
- Payment integration for RakerDivers
- Analytics and reporting
- Bulk operations for admins

## 🔗 Key Documentation Files

- `SUPABASE_SETUP.md` - Initial database setup
- `AUTHENTICATION_SETUP.md` - User roles and auth
- `IMAGE_UPLOAD_SETUP.md` - Image functionality
- `RAKERDIVER_SETUP.md` - Bulk turn-in system
- `STORAGE_SETUP_DASHBOARD.md` - Storage configuration
- `PROFILE_IMPORT_SETUP.md` - Profile import from Glide app

## 💡 Development Notes

### Recent Decisions
- Changed "Model" to "Mold" throughout the application
- Implemented return status tracking for admin workflow
- Added RakerDiver role for bulk disc collection operations
- Used Supabase Storage instead of external image hosting
- Implemented profile import system with legacy mapping support
- Added enhanced profile fields (PDGA, social media, avatars)
- Moved profile linking from database triggers to JavaScript for reliability
- Successfully imported 143 real user profiles from legacy Glide app
- Added Sources table for admin-managed disc found locations with dropdown selection
- Separated general source location from specific location details for better organization

### UI/UX Styling Guidelines
**All new components must follow the existing DiscFinder app design patterns:**

#### Modal Components
- Use `modal-overlay` for backdrop
- Use `modal-content` for main container
- Use `large-modal` class for wide modals (like admin interfaces)
- Use `modal-header` with `<h3>` and `close-button`
- Use `modal-body` for scrollable content

#### Forms
- Use `form-section` for grouped form areas
- Use `disc-form` class for form containers
- Use `form-row` for horizontal field groups
- Use `form-group` for individual fields
- Use `form-actions` for button groups
- Use `checkbox-label` for checkbox inputs

#### Buttons
- Use `button primary` for main actions
- Use `button secondary` for cancel/alternative actions
- Use `button small` for compact buttons in lists
- Never use custom Tailwind classes like `bg-blue-600` or `px-4 py-2`

#### Cards and Lists
- Use `turnin-card` for item containers
- Use `turnin-header` for card titles
- Use `turnin-actions` for action buttons in cards
- Use `detail-row` with `label` and `value` spans for data display
- Use `status-badge` with `verified`/`pending` classes for status indicators

#### Status Messages
- Use `status-message error` for error states
- Use `coming-soon` for empty states

**Always check existing components for styling patterns before creating new styles.**

### Code Patterns
- TypeScript interfaces for type safety
- Context-based state management for auth
- Service layer pattern in `lib/supabase.ts`
- Component-based architecture with reusable pieces

---

**For New Development Threads:** This file contains the complete current state of the DiscFinder project. All major features including profile import are implemented and working. **PROFILE IMPORT COMPLETED**: 143 real user profiles successfully imported from legacy Glide app with automatic linking when they sign up. Users can now sign up and get their imported data (roles, PDGA numbers, contact info) automatically linked. Focus on the "Next Steps" section for priorities.
